<template>
  <header class="dashboard-header">
    <!-- Top Section: Title and Action Buttons -->
    <div class="header-top">
      <div class="header-title-section">
        <h1 class="header-title">Simon<PERSON></h1>
        <p class="header-subtitle">Advanced Sims 4 Mod Management & Intelligence</p>
      </div>

      <div class="header-actions">
        <button
          class="action-btn action-btn--primary"
          @click="emit('analyze-folder')"
          :disabled="isLoading"
        >
          <svg class="action-btn-icon" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
          </svg>
          {{ isLoading ? 'Analyzing...' : 'Analyze Mods Folder' }}
        </button>
        <button
          v-if="hasModsData"
          class="action-btn action-btn--secondary"
          @click="emit('extract-thumbnails')"
          :disabled="isExtractingThumbnails"
        >
          <svg class="action-btn-icon" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
          </svg>
          {{ isExtractingThumbnails ? 'Extracting...' : 'Extract Thumbnails' }}
        </button>
        <button
          v-if="hasModsData"
          class="action-btn action-btn--secondary"
          @click="emit('export-results')"
        >
          <svg class="action-btn-icon" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
          </svg>
          Export
        </button>
        <button
          class="action-btn action-btn--secondary"
          @click="emit('open-settings')"
        >
          <svg class="action-btn-icon" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
          </svg>
          Settings
        </button>
        <div class="version-badge">v2.0.0</div>
      </div>
    </div>
  </header>
</template>

<script setup lang="ts">
interface DashboardHeaderProps {
  isLoading?: boolean;
  isExtractingThumbnails?: boolean;
  hasModsData?: boolean;
}

interface DashboardHeaderEmits {
  'analyze-folder': [];
  'extract-thumbnails': [];
  'export-results': [];
  'open-settings': [];
}

// Props
defineProps<DashboardHeaderProps>();

// Emits
const emit = defineEmits<DashboardHeaderEmits>();
</script>

<style scoped>
/* Import shared design system styles */
@import '../../styles/dashboard-shared.css';
@import '../../styles/simonitor-design-system.css';

.dashboard-header {
  background: linear-gradient(135deg, #2d1b69 0%, #1a0f3a 100%);
  border-radius: 16px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow:
    0 4px 20px rgba(45, 27, 105, 0.3),
    0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.header-top {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 24px;
}

.header-title-section {
  flex: 1;
}

.header-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: #ffffff;
  margin: 0 0 8px 0;
  letter-spacing: -0.025em;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.header-subtitle {
  font-size: 1.1rem;
  color: rgba(255, 255, 255, 0.8);
  margin: 0;
  font-weight: 400;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-wrap: wrap;
}

.action-btn {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  border-radius: 12px;
  font-weight: 600;
  font-size: 0.95rem;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  border: none;
  cursor: pointer;
  white-space: nowrap;
  position: relative;
  overflow: hidden;
}

.action-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none !important;
}

.action-btn--primary {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
}

.action-btn--primary:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(16, 185, 129, 0.4);
}

.action-btn--secondary {
  background: rgba(255, 255, 255, 0.1);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
}

.action-btn--secondary:hover:not(:disabled) {
  background: rgba(255, 255, 255, 0.15);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.action-btn-icon {
  width: 18px;
  height: 18px;
  flex-shrink: 0;
}

.version-badge {
  background: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.8);
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 500;
  border: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
}

/* Responsive Design */
@media (max-width: 768px) {
  .header-top {
    flex-direction: column;
    align-items: stretch;
    gap: 16px;
  }

  .header-actions {
    justify-content: center;
  }

  .action-btn {
    flex: 1;
    min-width: 0;
    justify-content: center;
  }

  .header-title {
    font-size: 2rem;
    text-align: center;
  }

  .header-subtitle {
    text-align: center;
  }
}

@media (max-width: 480px) {
  .dashboard-header {
    padding: 16px;
    margin-bottom: 16px;
  }

  .header-actions {
    flex-direction: column;
    gap: 8px;
  }

  .action-btn {
    width: 100%;
  }
}
</style>