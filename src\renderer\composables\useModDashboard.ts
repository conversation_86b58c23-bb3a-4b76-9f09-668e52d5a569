import { ref, computed, watch, shallowRef, shallowReactive, nextTick } from 'vue';
import type {
  ThumbnailSize,
  FileTypeFilter,
  QualityFilter,
  SortOption,
  ModData
} from '../types/dashboard';
import {
  SIZE_OPTIONS,
  DEFAULT_THUMBNAIL_SIZE,
  DEFAULT_ITEMS_PER_PAGE,
  DEFAULT_CURRENT_PAGE
} from '../constants/dashboard';

export function useModDashboard(props: { mods?: ModData[] }) {
  // Local reactive copy of mods that we can modify
  const localMods = ref<ModData[]>([]);

  // Use computed for reactive props - ensure reactivity
  const mods = computed(() => {
    return localMods.value.length > 0 ? localMods.value : (props.mods || []);
  });

  // Watch for prop changes and update local copy
  watch(() => props.mods, (newMods) => {
    if (newMods && newMods.length > 0 && localMods.value.length === 0) {
      // Only update if we don't have local mods yet (to preserve thumbnails)
      localMods.value = [...newMods];
    }
  }, { immediate: true });

  // Reactive state
  const searchQuery = ref('');
  const selectedFileTypeFilter = ref<FileTypeFilter>('');
  const selectedQualityFilter = ref<QualityFilter>('');
  const selectedSortOption = ref<SortOption>('name');
  const currentPage = ref(DEFAULT_CURRENT_PAGE);
  const itemsPerPage = ref(DEFAULT_ITEMS_PER_PAGE);

  // Thumbnail size control
  const thumbnailSize = ref<ThumbnailSize>(DEFAULT_THUMBNAIL_SIZE);
  const selectedMod = ref<ModData | null>(null);

  // Size slider reactive state
  const sizeSliderValue = computed(() => {
    return SIZE_OPTIONS.findIndex(option => option.value === thumbnailSize.value) + 1;
  });

  const currentSizeLabel = computed(() => {
    return SIZE_OPTIONS.find(option => option.value === thumbnailSize.value)?.label || 'SM';
  });

  // Collections that change frequently
  const modThumbnails = shallowRef<any[]>([]);
  const selectedMods = shallowReactive(new Set<string>());

  // Thumbnail extraction state
  const isExtractingThumbnails = ref(false);
  const thumbnailProgress = ref(0);

  // Image preview state
  const showImagePreview = ref(false);
  const previewThumbnailIndex = ref(0);

  // Computed properties
  const totalMods = computed(() => {
    return mods.value?.length || 0;
  });

  const hasActiveFilters = computed(() =>
    searchQuery.value ||
    selectedFileTypeFilter.value ||
    selectedQualityFilter.value
  );

  // Methods
  const clearSearch = () => {
    searchQuery.value = '';
  };

  const clearAllFilters = () => {
    searchQuery.value = '';
    selectedFileTypeFilter.value = '';
    selectedQualityFilter.value = '';
    currentPage.value = 1;
  };

  const handleSort = (field: SortOption) => {
    selectedSortOption.value = field;
    currentPage.value = 1;
  };

  const resetPagination = () => {
    currentPage.value = 1;
  };

  // Modal and interaction methods
  const openModDetails = (mod: ModData) => {
    console.log('🔍 [useModDashboard] openModDetails called with mod:', mod?.fileName || 'undefined');
    if (mod) {
      selectedMod.value = mod;
      nextTick(() => {
        console.log('🔄 [useModDashboard] After nextTick - selectedMod:', selectedMod.value?.fileName || 'null');
      });
    }
  };

  const closeModDetails = () => {
    console.log('🚪 [useModDashboard] closeModDetails called');
    selectedMod.value = null;
  };

  return {
    // State
    localMods,
    mods,
    searchQuery,
    selectedFileTypeFilter,
    selectedQualityFilter,
    selectedSortOption,
    currentPage,
    itemsPerPage,
    thumbnailSize,
    selectedMod,
    modThumbnails,
    selectedMods,
    isExtractingThumbnails,
    thumbnailProgress,
    showImagePreview,
    previewThumbnailIndex,

    // Computed
    totalMods,
    hasActiveFilters,
    sizeSliderValue,
    currentSizeLabel,

    // Methods
    clearSearch,
    clearAllFilters,
    handleSort,
    resetPagination,
    openModDetails,
    closeModDetails
  };
}