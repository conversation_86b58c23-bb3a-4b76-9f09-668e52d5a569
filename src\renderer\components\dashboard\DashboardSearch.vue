<template>
  <div class="dashboard-search">
    <div class="search-container">
      <div class="search-input-wrapper">
        <MagnifyingGlassIcon class="search-icon" />
        <input
          :value="searchQuery"
          @input="handleSearchInput"
          type="text"
          placeholder="Search your mod collection..."
          class="search-input"
          aria-label="Search mods"
        />
        <button
          v-if="searchQuery"
          @click="clearSearch"
          class="search-clear"
          aria-label="Clear search"
        >
          <XMarkIcon class="search-clear-icon" />
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { MagnifyingGlassIcon, XMarkIcon } from '@heroicons/vue/24/outline';
import type { UpdateEvent } from '../../types/dashboard';

interface DashboardSearchProps {
  searchQuery: string;
}

interface DashboardSearchEmits {
  'update:searchQuery': UpdateEvent<string>;
  'clear-search': [];
}

// Props
defineProps<DashboardSearchProps>();

// Emits
const emit = defineEmits<DashboardSearchEmits>();

// Methods
const handleSearchInput = (event: Event) => {
  const target = event.target as HTMLInputElement;
  emit('update:searchQuery', target.value);
};

const clearSearch = () => {
  emit('update:searchQuery', '');
  emit('clear-search');
};
</script>

<style scoped>
.dashboard-search {
  margin-bottom: 16px;
}

.search-container {
  display: flex;
  justify-content: center;
  width: 100%;
}

.search-input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  width: 100%;
  max-width: 600px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  backdrop-filter: blur(10px);
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.search-input-wrapper:focus-within {
  background: rgba(255, 255, 255, 0.15);
  border-color: rgba(16, 185, 129, 0.5);
  box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
}

.search-icon {
  position: absolute;
  left: 16px;
  width: 20px;
  height: 20px;
  color: rgba(255, 255, 255, 0.6);
  pointer-events: none;
  z-index: 1;
}

.search-input {
  flex: 1;
  padding: 16px 16px 16px 48px;
  background: transparent;
  border: none;
  color: white;
  font-size: 1rem;
  font-weight: 400;
  outline: none;
  width: 100%;
}

.search-input::placeholder {
  color: rgba(255, 255, 255, 0.5);
}

.search-clear {
  position: absolute;
  right: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  background: rgba(255, 255, 255, 0.1);
  border: none;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.search-clear:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: scale(1.05);
}

.search-clear-icon {
  width: 16px;
  height: 16px;
  color: rgba(255, 255, 255, 0.7);
}

/* Responsive Design */
@media (max-width: 768px) {
  .search-input-wrapper {
    max-width: none;
  }

  .search-input {
    padding: 14px 14px 14px 44px;
    font-size: 0.95rem;
  }

  .search-icon {
    left: 14px;
    width: 18px;
    height: 18px;
  }
}

@media (max-width: 480px) {
  .search-input {
    padding: 12px 12px 12px 40px;
    font-size: 0.9rem;
  }

  .search-icon {
    left: 12px;
    width: 16px;
    height: 16px;
  }

  .search-clear {
    right: 8px;
    width: 28px;
    height: 28px;
  }

  .search-clear-icon {
    width: 14px;
    height: 14px;
  }
}
</style>