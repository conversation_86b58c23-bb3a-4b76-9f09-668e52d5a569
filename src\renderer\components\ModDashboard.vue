<template>
  <div class="mod-dashboard">
    <!-- Dashboard Header Component -->
    <DashboardHeader
      :is-loading="isLoading"
      :is-extracting-thumbnails="isExtractingThumbnails"
      :has-mods-data="totalMods > 0"
      @analyze-folder="emit('analyze-folder')"
      @extract-thumbnails="extractThumbnails"
      @export-results="emit('export-results')"
      @open-settings="emit('open-settings')"
    />

    <!-- Main Dashboard Content -->
    <div v-if="totalMods > 0" class="dashboard-content">
      <!-- Search Component -->
      <DashboardSearch
        v-model:search-query="searchQuery"
        @clear-search="clearSearch"
      />

      <!-- Filters Component -->
      <DashboardFilters
        v-model:selected-file-type-filter="selectedFileTypeFilter"
        v-model:selected-quality-filter="selectedQualityFilter"
        v-model:selected-sort-option="selectedSortOption"
        :search-query="searchQuery"
        @clear-all-filters="clearAllFilters"
      />

      <!-- View Controls Component -->
      <DashboardViewControls
        v-model:thumbnail-size="thumbnailSize"
        :results-count="filteredMods.length"
        :has-active-filters="hasActiveFilters"
      />

      <!-- Stats Component -->
      <DashboardStats
        :total-count="totalMods"
        :filtered-count="filteredMods.length"
        :current-page="currentPage"
        :items-per-page="itemsPerPage"
        :has-active-filters="hasActiveFilters"
      />

      <!-- Mod Grid Component -->
      <ModGrid
        :mods="paginatedMods"
        :thumbnail-size="thumbnailSize"
        view-mode="grid"
        :has-active-filters="hasActiveFilters"
        @mod-click="openModDetails"
        @clear-all-filters="clearAllFilters"
      />

      <!-- Pagination Component -->
      <DashboardPagination
        v-model:current-page="currentPage"
        :total-pages="totalPages"
        :total-items="filteredMods.length"
        :items-per-page="itemsPerPage"
        @page-change="handlePageChange"
      />
    </div>

    <!-- Mod Details Modal -->
    <ModDetailsModal
      v-if="selectedMod"
      :mod="selectedMod"
      @close="closeModDetails"
    />
  </div>
</template>
<script setup lang="ts">
import { ref } from 'vue';
import type { ModData } from '../types/dashboard';
import { useModDashboard } from '../composables/useModDashboard';
import { useModFiltering } from '../composables/useModFiltering';
import { useModPagination } from '../composables/useModPagination';

// Import dashboard components
import DashboardHeader from './dashboard/DashboardHeader.vue';
import DashboardSearch from './dashboard/DashboardSearch.vue';
import DashboardFilters from './dashboard/DashboardFilters.vue';
import DashboardViewControls from './dashboard/DashboardViewControls.vue';
import DashboardStats from './dashboard/DashboardStats.vue';
import ModGrid from './dashboard/ModGrid.vue';
import DashboardPagination from './dashboard/DashboardPagination.vue';
import ModDetailsModal from './visual/ModDetailModal.vue';

// Props
interface ModDashboardProps {
  mods?: ModData[];
  isLoading?: boolean;
}

const props = withDefaults(defineProps<ModDashboardProps>(), {
  mods: () => [],
  isLoading: false
});

// Emits
interface ModDashboardEmits {
  'analyze-folder': [];
  'export-results': [];
  'open-settings': [];
}

const emit = defineEmits<ModDashboardEmits>();

// Use composables for state management
const dashboard = useModDashboard(props);
const filtering = useModFiltering(
  dashboard.mods,
  dashboard.searchQuery,
  dashboard.selectedFileTypeFilter,
  dashboard.selectedQualityFilter,
  dashboard.selectedSortOption
);
const pagination = useModPagination(
  filtering.filteredMods,
  dashboard.currentPage,
  dashboard.itemsPerPage
);

// Destructure composable returns for template access
const {
  // State
  searchQuery,
  selectedFileTypeFilter,
  selectedQualityFilter,
  selectedSortOption,
  currentPage,
  itemsPerPage,
  thumbnailSize,
  selectedMod,
  isExtractingThumbnails,

  // Computed
  totalMods,
  hasActiveFilters,

  // Methods
  clearSearch,
  clearAllFilters,
  openModDetails,
  closeModDetails
} = dashboard;

const { filteredMods } = filtering;
const { paginatedMods, totalPages } = pagination;

// Page change handler
const handlePageChange = (page: number) => {
  currentPage.value = page;
};

// Thumbnail extraction method
const extractThumbnails = async () => {
  if (!dashboard.mods.value || dashboard.mods.value.length === 0) {
    console.warn('No mods available for thumbnail extraction');
    return;
  }

  isExtractingThumbnails.value = true;

  try {
    console.log('🎨 [ModDashboard] Starting thumbnail extraction for', dashboard.mods.value.length, 'mods');

    for (let i = 0; i < dashboard.mods.value.length; i++) {
      const mod = dashboard.mods.value[i];

      if (!mod.filePath) {
        console.warn('⚠️ [ModDashboard] Skipping mod without filePath:', mod.fileName);
        continue;
      }

      try {
        // Use Electron IPC to extract thumbnails in the main process
        // Extract multiple thumbnails for color variations (especially RLE2)
        const result = await (window as any).electronAPI.extractThumbnails(mod.filePath, {
          maxThumbnails: 25, // Allow up to 25 thumbnails for hair color variations
          preferredFormat: 'webp',
          maxWidth: 256,
          maxHeight: 256,
          quality: 85
        });

        if (result.success && result.thumbnails && result.thumbnails.length > 0) {
          // Update the mod with extracted thumbnails
          dashboard.localMods.value[i] = {
            ...mod,
            thumbnails: result.thumbnails,
            primaryThumbnail: result.thumbnails[0],
            thumbnailVariations: result.thumbnails.slice(1),
            hasMultipleVariations: result.thumbnails.length > 1,
            thumbnailUrl: result.thumbnails[0]?.dataUrl || result.thumbnails[0]?.filePath
          };

          console.log(`✅ [ModDashboard] Extracted ${result.thumbnails.length} thumbnails for:`, mod.fileName);
        } else {
          console.log(`❌ [ModDashboard] No thumbnails found for:`, mod.fileName);
        }
      } catch (error) {
        console.error(`❌ [ModDashboard] Error extracting thumbnails for ${mod.fileName}:`, error);
      }
    }

    console.log('🎉 [ModDashboard] Thumbnail extraction completed');
  } catch (error) {
    console.error('❌ [ModDashboard] Error during thumbnail extraction:', error);
  } finally {
    isExtractingThumbnails.value = false;
  }
};
</script>

<style scoped>
/* Import shared design system styles */
@import '../styles/dashboard-shared.css';
@import '../styles/simonitor-design-system.css';

.mod-dashboard {
  padding: 20px;
  min-height: 100vh;
  background: linear-gradient(135deg, #1a0f3a 0%, #2d1b69 100%);
}

.dashboard-content {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .mod-dashboard {
    padding: 12px;
  }
}

@media (max-width: 480px) {
  .mod-dashboard {
    padding: 8px;
  }
}
</style>
