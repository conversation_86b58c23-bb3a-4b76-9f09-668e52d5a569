/**
 * Simonitor Strategic Color System v5.0
 * Vibrant gradient-based color palette for dynamic mod management
 * Designed with warm, energetic colors from lime green to deep orange-red
 */

/* ===== VIBRANT GRADIENT COLOR PALETTE ===== */
/* Based on the provided gradient: #8EA604 → #F5BB00 → #EC9F05 → #D76A03 → #BF3100 */

:root {
  /* ===== PRIMARY GRADIENT COLORS ===== */

  /* Lime Green - Primary Actions & Energy */
  /* Base: #8EA604 - Vibrant, energetic, growth-oriented */
  /* Psychology: Growth, energy, innovation, creativity */
  --lime-green-50: oklch(0.97 0.02 120);
  --lime-green-100: oklch(0.94 0.04 120);
  --lime-green-200: oklch(0.88 0.08 120);
  --lime-green-300: oklch(0.82 0.12 120);
  --lime-green-400: oklch(0.75 0.16 120);
  --lime-green-500: oklch(0.68 0.18 120); /* Base: #8EA604 */
  --lime-green-600: oklch(0.62 0.16 120);
  --lime-green-700: oklch(0.55 0.14 120);
  --lime-green-800: oklch(0.48 0.12 120);
  --lime-green-900: oklch(0.41 0.10 120);
  --lime-green-950: oklch(0.28 0.06 120);

  /* Golden Yellow - Secondary Actions & Warmth */
  /* Base: #F5BB00 - Warm, optimistic, attention-grabbing */
  /* Psychology: Optimism, creativity, warmth, energy */
  --golden-yellow-50: oklch(0.98 0.02 85);
  --golden-yellow-100: oklch(0.95 0.04 85);
  --golden-yellow-200: oklch(0.90 0.08 85);
  --golden-yellow-300: oklch(0.84 0.12 85);
  --golden-yellow-400: oklch(0.78 0.16 85);
  --golden-yellow-500: oklch(0.72 0.18 85); /* Base: #F5BB00 */
  --golden-yellow-600: oklch(0.66 0.16 85);
  --golden-yellow-700: oklch(0.59 0.14 85);
  --golden-yellow-800: oklch(0.52 0.12 85);
  --golden-yellow-900: oklch(0.45 0.10 85);
  --golden-yellow-950: oklch(0.32 0.06 85);

  /* Vibrant Orange - Warnings & Attention */
  /* Base: #EC9F05 - Energetic, attention-grabbing, dynamic */
  /* Psychology: Energy, enthusiasm, attention, creativity */
  --vibrant-orange-50: oklch(0.97 0.02 70);
  --vibrant-orange-100: oklch(0.94 0.04 70);
  --vibrant-orange-200: oklch(0.88 0.08 70);
  --vibrant-orange-300: oklch(0.81 0.12 70);
  --vibrant-orange-400: oklch(0.74 0.15 70);
  --vibrant-orange-500: oklch(0.68 0.16 70); /* Base: #EC9F05 */
  --vibrant-orange-600: oklch(0.61 0.15 70);
  --vibrant-orange-700: oklch(0.53 0.13 70);
  --vibrant-orange-800: oklch(0.45 0.11 70);
  --vibrant-orange-900: oklch(0.37 0.09 70);
  --vibrant-orange-950: oklch(0.24 0.06 70);

  /* Deep Orange - Serious Actions & Warnings */
  /* Base: #D76A03 - Strong, authoritative, important */
  /* Psychology: Authority, importance, serious attention needed */
  --deep-orange-50: oklch(0.97 0.02 55);
  --deep-orange-100: oklch(0.94 0.04 55);
  --deep-orange-200: oklch(0.88 0.08 55);
  --deep-orange-300: oklch(0.81 0.12 55);
  --deep-orange-400: oklch(0.74 0.15 55);
  --deep-orange-500: oklch(0.66 0.16 55); /* Base: #D76A03 */
  --deep-orange-600: oklch(0.59 0.15 55);
  --deep-orange-700: oklch(0.51 0.13 55);
  --deep-orange-800: oklch(0.43 0.11 55);
  --deep-orange-900: oklch(0.35 0.09 55);
  --deep-orange-950: oklch(0.22 0.06 55);

  /* Crimson Red - Errors & Critical Issues */
  /* Base: #BF3100 - Strong, urgent, critical attention */
  /* Psychology: Danger, urgency, critical problems, stop */
  --crimson-red-50: oklch(0.97 0.02 35);
  --crimson-red-100: oklch(0.94 0.04 35);
  --crimson-red-200: oklch(0.88 0.08 35);
  --crimson-red-300: oklch(0.81 0.12 35);
  --crimson-red-400: oklch(0.73 0.15 35);
  --crimson-red-500: oklch(0.58 0.16 35); /* Base: #BF3100 */
  --crimson-red-600: oklch(0.51 0.15 35);
  --crimson-red-700: oklch(0.43 0.13 35);
  --crimson-red-800: oklch(0.36 0.11 35);
  --crimson-red-900: oklch(0.29 0.09 35);
  --crimson-red-950: oklch(0.18 0.06 35);

  /* Sophisticated Grays - Neutral Elements */
  /* Apple-inspired minimalism */
  /* Psychology: Clean, professional, unobtrusive */
  --neutral-50: oklch(0.98 0.002 240);
  --neutral-100: oklch(0.96 0.002 240);
  --neutral-200: oklch(0.92 0.003 240);
  --neutral-300: oklch(0.86 0.004 240);
  --neutral-400: oklch(0.71 0.005 240);
  --neutral-500: oklch(0.56 0.006 240);
  --neutral-600: oklch(0.49 0.006 240);
  --neutral-700: oklch(0.42 0.005 240);
  --neutral-800: oklch(0.28 0.004 240);
  --neutral-900: oklch(0.19 0.003 240);
  --neutral-950: oklch(0.13 0.002 240);

  /* ===== SEMANTIC COLOR MAPPING ===== */
  
  /* Primary Brand Colors - Vibrant Lime Green */
  --primary: var(--lime-green-500);
  --primary-hover: var(--lime-green-600);
  --primary-active: var(--lime-green-700);
  --primary-foreground: var(--neutral-50);
  --primary-border: var(--lime-green-300);
  --primary-bg: var(--lime-green-50);

  /* Secondary Actions - Golden Yellow */
  --secondary: var(--golden-yellow-500);
  --secondary-hover: var(--golden-yellow-600);
  --secondary-active: var(--golden-yellow-700);
  --secondary-foreground: var(--neutral-900);
  --secondary-bg: var(--golden-yellow-50);
  --secondary-border: var(--golden-yellow-300);

  /* State Colors - Gradient Mapping */
  --success: var(--lime-green-500);
  --success-hover: var(--lime-green-600);
  --success-foreground: var(--neutral-50);
  --success-bg: var(--lime-green-50);

  --warning: var(--vibrant-orange-500);
  --warning-hover: var(--vibrant-orange-600);
  --warning-foreground: var(--neutral-50);
  --warning-bg: var(--vibrant-orange-50);

  --error: var(--crimson-red-500);
  --error-hover: var(--crimson-red-600);
  --error-foreground: var(--neutral-50);
  --error-bg: var(--crimson-red-50);

  --premium: var(--deep-orange-500);
  --premium-hover: var(--deep-orange-600);
  --premium-foreground: var(--neutral-50);
  --premium-bg: var(--deep-orange-50);

  /* Quality Score Colors - Gradient Progression */
  --quality-excellent: var(--lime-green-600);    /* 90-100% - Best quality */
  --quality-good: var(--golden-yellow-600);      /* 75-89% - Good quality */
  --quality-fair: var(--vibrant-orange-600);     /* 60-74% - Fair quality */
  --quality-poor: var(--crimson-red-600);        /* Below 60% - Poor quality */

  /* File Type Colors - Gradient Mapping */
  --file-package: var(--lime-green-500);         /* .package files - Primary */
  --file-script: var(--deep-orange-500);         /* .ts4script files - Premium */
  --file-resource: var(--vibrant-orange-500);    /* resource files - Warning */
  --file-unknown: var(--crimson-red-500);        /* unknown/problematic - Error */

  /* Background Colors */
  --bg-primary: var(--neutral-50);
  --bg-secondary: var(--neutral-100);
  --bg-elevated: var(--neutral-50);
  --bg-glass: oklch(from var(--neutral-50) l c h / 0.8);

  /* Text Colors */
  --text-primary: var(--neutral-900);
  --text-secondary: var(--neutral-600);
  --text-muted: var(--neutral-500);
  --text-inverse: var(--neutral-50);

  /* Border Colors */
  --border-light: var(--neutral-200);
  --border-medium: var(--neutral-300);
  --border-strong: var(--neutral-400);

  /* Focus Ring */
  --focus-ring: var(--primary);
  --focus-ring-offset: var(--neutral-50);
}

/* ===== DARK MODE VARIANTS ===== */
@media (prefers-color-scheme: dark) {
  :root {
    /* Dark mode background adjustments */
    --bg-primary: var(--neutral-950);
    --bg-secondary: var(--neutral-900);
    --bg-elevated: var(--neutral-800);
    --bg-glass: oklch(from var(--neutral-900) l c h / 0.8);

    /* Dark mode text adjustments */
    --text-primary: var(--neutral-50);
    --text-secondary: var(--neutral-300);
    --text-muted: var(--neutral-400);
    --text-inverse: var(--neutral-900);

    /* Dark mode border adjustments */
    --border-light: var(--neutral-800);
    --border-medium: var(--neutral-700);
    --border-strong: var(--neutral-600);

    /* Dark mode focus ring */
    --focus-ring-offset: var(--neutral-950);
  }
}

/* ===== HIGH CONTRAST MODE ===== */
@media (prefers-contrast: high) {
  :root {
    --border-light: var(--neutral-600);
    --border-medium: var(--neutral-700);
    --border-strong: var(--neutral-800);
  }
}
