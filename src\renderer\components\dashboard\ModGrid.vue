<template>
  <div class="mod-grid">
    <!-- Thumbnail Grid (Primary View) -->
    <div v-if="mods.length > 0" class="thumbnail-gallery">
      <div
        class="thumbnail-grid"
        :class="[
          `thumbnail-grid--${thumbnailSize}`,
          'thumbnail-grid--loaded'
        ]"
        role="grid"
        aria-label="Mod collection"
      >
        <!-- Use ModThumbnailCard component for enhanced thumbnail support -->
        <ModThumbnailCard
          v-for="(mod, index) in mods"
          :key="mod.fileName || index"
          :mod="mod"
          :view-mode="viewMode"
          :style="{ animationDelay: `${index * 50}ms` }"
          @click="handleModClick"
          class="thumbnail-item thumbnail-item--loaded"
        />
      </div>
    </div>

    <!-- Empty State -->
    <div v-else class="empty-state">
      <div class="empty-state__icon">
        <FolderOpenIcon />
      </div>
      <h2 class="empty-state__title">No mods found</h2>
      <p class="empty-state__description">
        {{ hasActiveFilters ?
          'Try adjusting your filters to see more results.' :
          'Start by analyzing your Sims 4 mods folder to explore your collection.' }}
      </p>
      <button v-if="hasActiveFilters" @click="emit('clear-all-filters')" class="empty-state__action">
        Clear All Filters
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { FolderOpenIcon } from '@heroicons/vue/24/outline';
import ModThumbnailCard from '../visual/ModThumbnailCard.vue';
import type { ThumbnailSize, ViewMode, ModData, ModClickEvent } from '../../types/dashboard';

interface ModGridProps {
  mods: ModData[];
  thumbnailSize: ThumbnailSize;
  viewMode: ViewMode;
  hasActiveFilters: boolean;
}

interface ModGridEmits {
  'mod-click': ModClickEvent;
  'clear-all-filters': [];
}

// Props
defineProps<ModGridProps>();

// Emits
const emit = defineEmits<ModGridEmits>();

// Methods
const handleModClick = (mod: ModData) => {
  emit('mod-click', mod);
};
</script>

<style scoped>
.mod-grid {
  width: 100%;
}

.thumbnail-gallery {
  width: 100%;
}

.thumbnail-grid {
  display: grid;
  gap: 16px;
  width: 100%;
  padding: 0;
  margin: 0;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Grid size variations */
.thumbnail-grid--xs {
  grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
  gap: 12px;
}

.thumbnail-grid--sm {
  grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
  gap: 14px;
}

.thumbnail-grid--md {
  grid-template-columns: repeat(auto-fill, minmax(260px, 1fr));
  gap: 16px;
}

.thumbnail-grid--lg {
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 18px;
}

.thumbnail-grid--xl {
  grid-template-columns: repeat(auto-fill, minmax(340px, 1fr));
  gap: 20px;
}

.thumbnail-grid--loaded {
  opacity: 1;
}

.thumbnail-item {
  opacity: 0;
  transform: translateY(20px);
  animation: fadeInUp 0.6s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

.thumbnail-item--loaded {
  opacity: 1;
  transform: translateY(0);
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Empty State */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 64px 24px;
  text-align: center;
  min-height: 400px;
}

.empty-state__icon {
  width: 80px;
  height: 80px;
  margin-bottom: 24px;
  color: rgba(255, 255, 255, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
}

.empty-state__icon svg {
  width: 100%;
  height: 100%;
}

.empty-state__title {
  font-size: 1.5rem;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.9);
  margin: 0 0 12px 0;
}

.empty-state__description {
  font-size: 1rem;
  color: rgba(255, 255, 255, 0.6);
  margin: 0 0 24px 0;
  max-width: 400px;
  line-height: 1.5;
}

.empty-state__action {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 12px 24px;
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
  border: none;
  border-radius: 12px;
  font-weight: 600;
  font-size: 0.95rem;
  cursor: pointer;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
}

.empty-state__action:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(16, 185, 129, 0.4);
}

.empty-state__action:active {
  transform: translateY(0);
}

/* Responsive Design */
@media (max-width: 768px) {
  .thumbnail-grid--xs {
    grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
    gap: 10px;
  }

  .thumbnail-grid--sm {
    grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
    gap: 12px;
  }

  .thumbnail-grid--md {
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 14px;
  }

  .thumbnail-grid--lg {
    grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
    gap: 16px;
  }

  .thumbnail-grid--xl {
    grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
    gap: 18px;
  }

  .empty-state {
    padding: 48px 16px;
    min-height: 300px;
  }

  .empty-state__icon {
    width: 64px;
    height: 64px;
    margin-bottom: 20px;
  }

  .empty-state__title {
    font-size: 1.25rem;
  }

  .empty-state__description {
    font-size: 0.9rem;
  }
}

@media (max-width: 480px) {
  .thumbnail-grid--xs,
  .thumbnail-grid--sm {
    grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
    gap: 8px;
  }

  .thumbnail-grid--md,
  .thumbnail-grid--lg,
  .thumbnail-grid--xl {
    grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
    gap: 10px;
  }

  .empty-state {
    padding: 32px 12px;
    min-height: 250px;
  }

  .empty-state__icon {
    width: 48px;
    height: 48px;
    margin-bottom: 16px;
  }

  .empty-state__title {
    font-size: 1.1rem;
  }

  .empty-state__description {
    font-size: 0.85rem;
  }

  .empty-state__action {
    padding: 10px 20px;
    font-size: 0.9rem;
  }
}
</style>