<template>
  <div :class="['mod-thumbnail-card', viewMode, { selected: isSelected }]" @click="handleClick">
    <!-- Thumbnail Section -->
    <div class="thumbnail-container">
      <div v-if="thumbnailLoading" class="thumbnail-loading">
        <div class="loading-spinner"></div>
      </div>

      <!-- Primary Thumbnail Display (main card shows only primary) -->
      <img
        v-if="thumbnailUrl"
        :src="thumbnailUrl"
        :alt="`${mod.fileName} thumbnail`"
        class="thumbnail-image"
        @load="handleThumbnailLoad"
        @error="handleThumbnailError"
      />

      <div v-else class="thumbnail-placeholder">
        <component :is="getPlaceholderIcon()" class="placeholder-icon" />
        <span class="placeholder-text">{{ getPlaceholderText() }}</span>
      </div>

      <!-- Hover-activated text overlay with all content -->
      <div class="hover-text-overlay">
        <!-- Top section: Status badges -->
        <div class="overlay-top">
          <div class="badge-container">
            <span v-if="mod.hasConflicts" class="badge badge-warning" title="Has conflicts">
              <ExclamationTriangleIcon class="w-3 h-3" />
            </span>
            <span v-if="mod.isCorrupted" class="badge badge-error" title="Corrupted file">
              <XCircleIcon class="w-3 h-3" />
            </span>
            <span v-if="isHighQuality" class="badge badge-success" title="High quality">
              <SparklesIcon class="w-3 h-3" />
            </span>
          </div>

          <!-- Variation indicator badge (shows if multiple variations exist) -->
          <div v-if="hasMultipleVariations" class="variation-indicator" title="Multiple color variations available">
            <span class="variation-count">+{{ thumbnailVariations.length }}</span>
          </div>
        </div>

        <!-- Bottom section: Title and metadata -->
        <div class="overlay-bottom">
          <h3 class="overlay-title" :title="mod.fileName">{{ displayName }}</h3>
          <div class="overlay-meta">
            <span class="category-badge" :class="getCategoryClass()">
              {{ mod.category || 'Unknown' }}
            </span>
            <span class="file-size">{{ formatFileSize(mod.fileSize) }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- List View Content (shown outside thumbnail for list layout) -->
    <div v-if="viewMode === 'list'" class="list-content">
      <div class="list-header">
        <h3 class="list-title" :title="mod.fileName">{{ displayName }}</h3>
        <div class="list-badges">
          <span v-if="mod.hasConflicts" class="list-badge badge-warning" title="Has conflicts">
            <ExclamationTriangleIcon class="w-3 h-3" />
          </span>
          <span v-if="mod.isCorrupted" class="list-badge badge-error" title="Corrupted file">
            <XCircleIcon class="w-3 h-3" />
          </span>
          <span v-if="isHighQuality" class="list-badge badge-success" title="High quality">
            <SparklesIcon class="w-3 h-3" />
          </span>
        </div>
      </div>
      <div class="list-meta">
        <span class="list-category-badge" :class="getCategoryClass()">
          {{ mod.category || 'Unknown' }}
        </span>
        <span class="list-file-size">{{ formatFileSize(mod.fileSize) }}</span>
        <span v-if="hasMultipleVariations" class="list-variation-indicator">
          +{{ thumbnailVariations.length }} variations
        </span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch } from 'vue';
import {
  ExclamationTriangleIcon,
  XCircleIcon,
  SparklesIcon,
  HeartIcon,
  EllipsisVerticalIcon,
  FolderOpenIcon,
  ClipboardIcon,
  InformationCircleIcon,
  TrashIcon,
  DocumentIcon,
  UserIcon,
  HomeIcon,
  CogIcon,
  PuzzlePieceIcon
} from '@heroicons/vue/24/outline';

import type { ModData } from '../../../types/ModData';
import type { ThumbnailData } from '../../../services/visual/ThumbnailExtractionService';
import ThumbnailVariationDisplay from './ThumbnailVariationDisplay.vue';

// Props
interface Props {
  mod: ModData;
  viewMode: 'grid' | 'list';
  isSelected?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  isSelected: false
});

// Emits
const emit = defineEmits<{
  click: [mod: ModData];
  thumbnailError: [modId: string];
  favorite: [modId: string, isFavorite: boolean];
  delete: [modId: string];
}>();

// Reactive state
const thumbnailLoading = ref(false);

// Computed thumbnail URL from enhanced data
const thumbnailUrl = computed(() => {
  // Use enhanced thumbnail data if available (imageData is the correct property)
  if (primaryThumbnail.value?.imageData) {
    console.log(`🎯 [ModThumbnailCard] ${props.mod.fileName} - Using primaryThumbnail.imageData`);
    return primaryThumbnail.value.imageData;
  }
  // Fallback to legacy thumbnail data
  if (props.mod.thumbnailData) {
    console.log(`🎯 [ModThumbnailCard] ${props.mod.fileName} - Using mod.thumbnailData`);
    return props.mod.thumbnailData;
  }
  if (props.mod.thumbnailUrl) {
    console.log(`🎯 [ModThumbnailCard] ${props.mod.fileName} - Using mod.thumbnailUrl`);
    return props.mod.thumbnailUrl;
  }
  console.log(`❌ [ModThumbnailCard] ${props.mod.fileName} - No thumbnail data found`);
  return null;
});
const showQuickActions = ref(false);
const isFavorite = ref(false);
const isAnalyzing = ref(false);
const analysisProgress = ref(0);

// Computed properties
const displayName = computed(() => {
  const name = props.mod.fileName;
  // Remove file extension and clean up the name
  return name.replace(/\.(package|ts4script)$/i, '').replace(/_/g, ' ');
});

const isHighQuality = computed(() => {
  // Determine if this is a high-quality mod based on various factors
  const hasHighResources = (props.mod.resourceCount || 0) > 50;
  const hasLargeSize = (props.mod.fileSize || 0) > 1024 * 1024; // > 1MB
  const hasEnhancedAnalysis = props.mod.objectClassification?.confidence > 0.8;
  
  return hasHighResources || hasLargeSize || hasEnhancedAnalysis;
});

const hasEnhancedContent = computed(() => {
  return props.mod.objectClassification || props.mod.universalClassification;
});

// Enhanced thumbnail support
const hasThumbnails = computed(() => {
  const result = props.mod.thumbnails && props.mod.thumbnails.length > 0;
  console.log(`🔍 [ModThumbnailCard] ${props.mod.fileName} - hasThumbnails: ${result}, thumbnails: ${props.mod.thumbnails?.length || 0}`);
  return result;
});

const primaryThumbnail = computed(() => {
  const result = props.mod.primaryThumbnail || (props.mod.thumbnails && props.mod.thumbnails[0]);
  console.log(`🎯 [ModThumbnailCard] ${props.mod.fileName} - primaryThumbnail: ${result ? 'Yes' : 'No'}`);
  return result;
});

const thumbnailVariations = computed(() => {
  const result = props.mod.thumbnailVariations || [];
  console.log(`🎨 [ModThumbnailCard] ${props.mod.fileName} - variations: ${result.length}`);
  return result;
});

const hasMultipleVariations = computed(() => {
  const result = props.mod.hasMultipleVariations || thumbnailVariations.value.length > 0;
  console.log(`🔢 [ModThumbnailCard] ${props.mod.fileName} - hasMultipleVariations: ${result}`);
  return result;
});

// Methods
const handleClick = () => {
  emit('click', props.mod);
};

const handleThumbnailLoad = () => {
  thumbnailLoading.value = false;
};

const handleThumbnailError = () => {
  thumbnailLoading.value = false;
  // thumbnailUrl is now computed, can't set it directly
  emit('thumbnailError', props.mod.id);
};

const toggleFavorite = () => {
  isFavorite.value = !isFavorite.value;
  emit('favorite', props.mod.id, isFavorite.value);
};

const openInExplorer = () => {
  // Emit event to parent to handle file explorer opening
  window.electronAPI?.openInExplorer?.(props.mod.filePath);
  showQuickActions.value = false;
};

const copyPath = async () => {
  try {
    await navigator.clipboard.writeText(props.mod.filePath);
    // Could show a toast notification here
  } catch (error) {
    console.error('Failed to copy path:', error);
  }
  showQuickActions.value = false;
};

const showDetails = () => {
  emit('click', props.mod);
  showQuickActions.value = false;
};

// Enhanced thumbnail event handlers
const handleThumbnailSelected = (thumbnail: ThumbnailData) => {
  console.log(`Selected thumbnail variation: ${thumbnail.resourceType}`);
  // TODO: Implement thumbnail selection logic
  // For now, just log the selection
};

const handleVariationHover = (thumbnail: ThumbnailData) => {
  // Optional: Preview thumbnail on hover
  console.log(`Hovering over variation: ${thumbnail.resourceType}`);
};

const handleVariationLeave = () => {
  // Optional: Reset preview on hover leave
};

const deleteMod = () => {
  if (confirm(`Are you sure you want to delete "${props.mod.fileName}"?`)) {
    emit('delete', props.mod.id);
  }
  showQuickActions.value = false;
};

const getPlaceholderIcon = () => {
  const category = props.mod.category?.toLowerCase() || '';
  
  if (category.includes('cas') || category.includes('create-a-sim')) return UserIcon;
  if (category.includes('build') || category.includes('buy')) return HomeIcon;
  if (category.includes('script')) return CogIcon;
  if (category.includes('gameplay')) return PuzzlePieceIcon;
  
  return DocumentIcon;
};

const getPlaceholderText = () => {
  const category = props.mod.category?.toLowerCase() || '';
  
  if (category.includes('cas')) return 'CAS';
  if (category.includes('build') || category.includes('buy')) return 'Build/Buy';
  if (category.includes('script')) return 'Script';
  if (category.includes('gameplay')) return 'Gameplay';
  
  return 'Mod';
};

const getCategoryClass = () => {
  const category = props.mod.category?.toLowerCase() || '';
  
  if (category.includes('cas') || category.includes('create-a-sim')) return 'category-cas';
  if (category.includes('build') || category.includes('buy')) return 'category-build';
  if (category.includes('script')) return 'category-script';
  if (category.includes('gameplay')) return 'category-gameplay';
  
  return 'category-other';
};

const getContentTags = (): string[] => {
  const tags: string[] = [];
  
  if (props.mod.objectClassification) {
    if (props.mod.objectClassification.category) {
      tags.push(props.mod.objectClassification.category);
    }
    if (props.mod.objectClassification.functionality) {
      tags.push(...props.mod.objectClassification.functionality);
    }
  }
  
  if (props.mod.universalClassification) {
    if (props.mod.universalClassification.subcategory) {
      tags.push(props.mod.universalClassification.subcategory);
    }
    if (props.mod.universalClassification.ageGroups) {
      tags.push(...props.mod.universalClassification.ageGroups);
    }
  }
  
  return tags.slice(0, 3); // Limit to 3 tags for display
};

const formatFileSize = (bytes: number | undefined): string => {
  if (!bytes) return '0 B';
  
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(1024));
  return `${(bytes / Math.pow(1024, i)).toFixed(1)} ${sizes[i]}`;
};

const formatDate = (timestamp: number | undefined): string => {
  if (!timestamp) return 'Unknown';
  
  const date = new Date(timestamp);
  return date.toLocaleDateString();
};

const loadThumbnail = () => {
  // Thumbnail loading is now handled by ModDashboard via IPC
  // This component just displays the data provided by the parent
  console.log(`🖼️ [ModThumbnailCard] Loading thumbnail for ${props.mod.fileName}:`, {
    hasThumbnailUrl: !!thumbnailUrl.value,
    hasThumbnails: hasThumbnails.value,
    hasVariations: hasMultipleVariations.value
  });
};

// Lifecycle
onMounted(() => {
  console.log(`🚀 [ModThumbnailCard] Mounted for ${props.mod.fileName}:`, {
    hasThumbnails: props.mod.thumbnails?.length || 0,
    hasVariations: props.mod.thumbnailVariations?.length || 0,
    hasMultipleVariations: props.mod.hasMultipleVariations,
    primaryThumbnail: !!props.mod.primaryThumbnail,
    thumbnailData: !!props.mod.thumbnailData,
    filePath: !!props.mod.filePath
  });
  loadThumbnail();
});

// Close quick actions when clicking outside
const handleClickOutside = (event: Event) => {
  if (!event.target || !(event.target as Element).closest('.quick-actions-menu')) {
    showQuickActions.value = false;
  }
};

onMounted(() => {
  document.addEventListener('click', handleClickOutside);
});

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside);
});
</script>

<style scoped>
/* Import shared design system styles */
@import '../../styles/dashboard-shared.css';
@import '../../styles/simonitor-design-system.css';

/* Professional Card Design - Apple-inspired with Sims 4 theming */
.mod-thumbnail-card {
  @apply cursor-pointer overflow-hidden transition-all duration-300 ease-out relative;

  /* Clean, image-focused design with strategic color system */
  background: var(--bg-elevated);
  border-radius: 10px; /* Perfect square with rounded corners */
  border: 1px solid var(--border-light);

  /* Subtle shadow for depth without distraction */
  box-shadow: var(--shadow-sm);

  /* Performance optimizations */
  transform: translateZ(0);
  backface-visibility: hidden;
  will-change: transform, box-shadow, border-color;

  /* Enhanced visual hierarchy */
  position: relative;
  isolation: isolate;
}

.mod-thumbnail-card:hover {
  /* Clean, subtle hover animation focusing on the image */
  transform: translateY(-4px) scale(1.02) translateZ(0);
  border-color: var(--primary-border);

  /* Enhanced depth with strategic color system */
  box-shadow: var(--shadow-lg);

  /* Clean background enhancement */
  background: var(--bg-elevated);
}

.mod-thumbnail-card:active {
  /* Refined press feedback */
  transform: translateY(-4px) scale(1.01) translateZ(0);
  transition-duration: 150ms;
  box-shadow: var(--shadow-md);
}

.mod-thumbnail-card.selected {
  /* Premium selection state with strategic colors */
  border: 2px solid var(--primary);
  transform: translateY(-6px) scale(1.03) translateZ(0);

  /* Sophisticated selection glow with strategic colors */
  box-shadow: var(--shadow-xl);

  /* Subtle branded background */
  background: linear-gradient(145deg,
    var(--primary-bg) 0%,
    var(--bg-elevated) 50%,
    var(--primary-bg) 100%);
}

.mod-thumbnail-card.selected:hover {
  /* Enhanced selection hover with premium feel */
  transform: translateY(-8px) scale(1.04) translateZ(0);
  box-shadow: var(--shadow-2xl);
}

.mod-thumbnail-card.list {
  @apply flex items-center space-x-4;
  padding: var(--space-4);
  border-radius: var(--radius-lg);
  height: auto;
  aspect-ratio: unset;
}

/* List view - show content outside overlay */
.mod-thumbnail-card.list .hover-text-overlay {
  display: none; /* Hide overlay in list view */
}

/* List view content styles */
.list-content {
  @apply flex-1 ml-4;
}

.list-header {
  @apply flex items-center justify-between mb-2;
}

.list-title {
  @apply text-base font-semibold;
  color: var(--text-primary);
  line-height: 1.4;

  /* Text overflow handling */
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.list-badges {
  @apply flex space-x-1;
}

.list-badge {
  @apply flex items-center px-2 py-1 rounded-full text-xs font-medium;
}

.list-meta {
  @apply flex items-center space-x-3 text-sm;
}

.list-category-badge {
  @apply px-2 py-1 rounded-full text-xs font-medium;
  text-transform: uppercase;
  letter-spacing: 0.04em;
}

.list-file-size {
  @apply text-xs;
  color: var(--text-muted);
  font-weight: 500;
}

.list-variation-indicator {
  @apply text-xs px-2 py-1 rounded-full;
  background: var(--primary-bg);
  color: var(--primary);
  font-weight: 600;
}

.mod-thumbnail-card.grid {
  /* Perfect square aspect ratio for clean, image-focused design */
  aspect-ratio: 1;
  width: 100%;
  position: relative;
}

/* Clean Thumbnail Container - Image-focused design */
.thumbnail-container {
  @apply relative overflow-hidden;
  background: var(--bg-secondary);
  position: relative;
  isolation: isolate;
}

.mod-thumbnail-card.grid .thumbnail-container {
  /* Perfect square aspect ratio - fills entire card */
  aspect-ratio: 1;
  width: 100%;
  height: 100%;
  border-radius: 10px; /* Match card border radius completely */
  overflow: hidden;
}

.mod-thumbnail-card.list .thumbnail-container {
  @apply w-20 h-20 flex-shrink-0;
  border-radius: 8px; /* Slightly smaller radius for list view */
  box-shadow: var(--shadow-subtle);
  aspect-ratio: 1; /* Ensure square even in list view */
}

.thumbnail-loading {
  @apply w-full h-full flex items-center justify-center bg-gray-100 dark:bg-gray-700;
}

.loading-spinner {
  @apply w-6 h-6 border-2 border-gray-300 border-t-green-500 rounded-full animate-spin;
}

.thumbnail-image {
  @apply w-full h-full object-cover transition-all duration-300 ease-out;
  position: relative;
  z-index: 1;
  object-position: center center; /* Center the image for optimal cropping */
  /* Ensure image fills square container without distortion */
  object-fit: cover;
}

.mod-thumbnail-card:hover .thumbnail-image {
  transform: scale(1.05);
  filter: brightness(1.02) contrast(1.01) saturate(1.05);
}

.thumbnail-placeholder {
  @apply w-full h-full flex flex-col items-center justify-center
         text-gray-500 dark:text-gray-400 transition-all duration-500;
  background: linear-gradient(135deg,
    rgba(248, 250, 252, 0.95) 0%,
    rgba(241, 245, 249, 1) 50%,
    rgba(226, 232, 240, 0.95) 100%);
  backdrop-filter: blur(20px);
  position: relative;
  z-index: 2;
}

.thumbnail-placeholder::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 60%;
  height: 60%;
  background: radial-gradient(circle,
    rgba(45, 159, 43, 0.03) 0%,
    transparent 70%);
  transform: translate(-50%, -50%);
  border-radius: 50%;
  opacity: 0;
  transition: opacity 0.3s ease-out;
}

.mod-thumbnail-card:hover .thumbnail-placeholder::before {
  opacity: 1;
}

.mod-thumbnail-card:hover .thumbnail-placeholder {
  background: linear-gradient(135deg,
    rgba(45, 159, 43, 0.08) 0%,
    rgba(45, 159, 43, 0.12) 50%,
    rgba(45, 159, 43, 0.08) 100%);
  color: rgba(45, 159, 43, 0.8);
}

.placeholder-icon {
  @apply w-8 h-8 mb-2;
}

.mod-thumbnail-card.list .placeholder-icon {
  @apply w-6 h-6 mb-1;
}

.placeholder-text {
  @apply text-sm font-medium;
}

.mod-thumbnail-card.list .placeholder-text {
  @apply text-xs;
}

/* Modern Hover-Activated Text Overlay - Grid View Only */
.hover-text-overlay {
  @apply absolute inset-0 opacity-0 transition-all duration-400 ease-out
         flex flex-col justify-between p-3;
  background: linear-gradient(180deg,
    rgba(0, 0, 0, 0.85) 0%,
    rgba(0, 0, 0, 0.6) 40%,
    rgba(0, 0, 0, 0.9) 100%);
  pointer-events: none; /* Allow clicks to pass through to main card */
  backdrop-filter: blur(12px);
  z-index: 5;
  border-radius: 10px; /* Match card border radius */
}

.mod-thumbnail-card.grid:hover .hover-text-overlay {
  @apply opacity-100;
}

/* Overlay Top Section - Status badges and indicators */
.overlay-top {
  @apply flex items-start justify-between;
}

/* Overlay Bottom Section - Title and metadata */
.overlay-bottom {
  @apply flex flex-col space-y-2;
}

/* Overlay Title Styling */
.overlay-title {
  @apply text-white font-bold text-sm leading-tight;
  text-shadow: 0 2px 8px rgba(0, 0, 0, 0.8), 0 1px 3px rgba(0, 0, 0, 0.9);

  /* Advanced text overflow handling */
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  margin-bottom: 0.5rem;
}

/* Overlay Metadata Styling */
.overlay-meta {
  @apply flex items-center justify-between gap-2;
}

.badge-container {
  @apply flex space-x-2;
}

.badge {
  @apply flex items-center px-2 py-1 rounded-full text-xs font-bold
         transition-all duration-300 ease-out;
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.4);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.4), 0 1px 3px rgba(0, 0, 0, 0.6);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.8);
}

.badge-warning {
  background: var(--warning);
  color: white;
}

.badge-error {
  background: var(--error);
  color: white;
}

.badge-success {
  background: var(--success);
  color: white;
}

/* Enhanced action buttons with Apple-inspired micro-interactions */
.action-buttons {
  @apply flex space-x-2 self-end opacity-0 transition-all duration-300 ease-out;
  transform: translateY(4px);
}

.mod-thumbnail-card:hover .action-buttons {
  @apply opacity-100;
  transform: translateY(0);
}

.action-btn {
  @apply p-2 bg-white dark:bg-gray-800 text-gray-600 dark:text-gray-300
         rounded-full shadow-sm hover:shadow-md transition-all duration-200 ease-out;
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.9);
}

.action-btn:hover {
  @apply bg-white dark:bg-gray-700;
  transform: scale(1.05);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.12);
}

.action-btn.active {
  @apply text-red-500;
  background: rgba(239, 68, 68, 0.1);
}

/* Quick Actions Menu */
.quick-actions-menu {
  @apply absolute top-full right-0 mt-1 bg-white dark:bg-gray-800 
         border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg
         py-1 z-10 min-w-40;
}

.quick-action {
  @apply w-full flex items-center space-x-2 px-3 py-2 text-sm
         text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700
         transition-colors;
}

.quick-action.danger {
  @apply text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900;
}

/* Grid cards are now purely image-focused - no separate content section needed */

/* Old content styles removed - content now shown in hover overlay */

/* Professional badges with strategic color system - Overlay optimized */
.category-badge {
  @apply px-2 py-1 rounded-full text-xs font-bold;
  letter-spacing: 0.04em;
  text-transform: uppercase;
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.4);
  color: white;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.8);

  /* Smooth transitions with professional easing */
  transition: all 0.3s ease-out;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.4), 0 1px 3px rgba(0, 0, 0, 0.6);
}

.category-cas {
  /* Strategic purple for CAS content */
  background: var(--premium);
}

.category-build {
  /* Strategic blue for Build/Buy content */
  background: var(--primary);
}

.category-script {
  /* Strategic green for script content */
  background: var(--success);
}

.category-gameplay {
  /* Strategic orange for gameplay content */
  background: var(--warning);
}

.category-other {
  background: var(--secondary);
}

.file-size {
  @apply text-xs font-medium;
  color: rgba(255, 255, 255, 0.9);
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.8);
}

/* List View Details */
.mod-details {
  @apply mt-2;
}

.mod-description {
  @apply text-sm text-gray-600 dark:text-gray-400 mb-2 line-clamp-2;
}

.mod-stats {
  @apply flex space-x-4 text-xs text-gray-500 dark:text-gray-400;
}

.stat-item {
  @apply flex space-x-1;
}

.stat-label {
  @apply font-medium;
}

/* Enhanced Content */
.enhanced-content {
  @apply mt-2 space-y-2;
}

.content-tags {
  @apply flex flex-wrap gap-1;
}

.content-tag {
  @apply px-2 py-1 bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200
         text-xs rounded-full;
}

.object-info {
  @apply text-sm text-gray-600 dark:text-gray-400;
}

.object-type {
  @apply font-medium;
}

.room-assignment {
  @apply text-gray-500 dark:text-gray-500;
}

/* Analysis Progress */
.analysis-progress {
  @apply mt-2;
}

.progress-bar {
  @apply w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2 mb-1;
}

.progress-fill {
  @apply bg-green-500 h-2 rounded-full transition-all duration-300;
}

.progress-text {
  @apply text-xs text-gray-500 dark:text-gray-400;
}

/* Premium variation indicator - positioned in overlay */
.variation-indicator {
  @apply text-xs font-bold px-3 py-1.5 rounded-full transition-all duration-300 ease-out;
  background: var(--primary);
  color: white;
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.4);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.4), 0 1px 3px rgba(0, 0, 0, 0.6);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.8);
}

.variation-count {
  font-weight: 900;
}

/* Professional Focus States for Accessibility */
.mod-thumbnail-card:focus-visible {
  outline: none;
  border-color: #2D9F2B;
  box-shadow:
    0 0 0 3px rgba(45, 159, 43, 0.2),
    0 8px 25px rgba(0, 0, 0, 0.08),
    0 4px 12px rgba(0, 0, 0, 0.06);
  transform: translateY(-2px) scale(1.01);
}

/* Dark mode enhancements */
@media (prefers-color-scheme: dark) {
  .mod-thumbnail-card {
    background: #1a1a1a;
    border-color: rgba(255, 255, 255, 0.08);
  }

  .mod-title {
    color: #ffffff;
  }

  .mod-thumbnail-card:hover .mod-title {
    color: #22C55E;
  }

  /* Dark mode content section removed - using hover overlay only */

  .thumbnail-placeholder {
    background: linear-gradient(135deg,
      rgba(38, 38, 38, 0.8) 0%,
      rgba(45, 45, 45, 0.9) 50%,
      rgba(38, 38, 38, 0.8) 100%);
  }
}
</style>
