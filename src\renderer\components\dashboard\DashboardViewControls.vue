<template>
  <div class="dashboard-view-controls">
    <div class="header-controls">
      <div class="results-info">
        <span class="results-count">{{ resultsCount }}</span>
        <span class="results-text">mods</span>
        <span v-if="hasActiveFilters" class="results-filtered">(filtered)</span>
      </div>

      <div class="view-controls">
        <!-- Enhanced Size Control Slider -->
        <div class="thumbnail-size-controls">
          <div class="size-control-group">
            <label for="size-slider" class="size-label">
              <Squares2X2Icon class="size-icon" />
              <span class="sr-only">Thumbnail size</span>
            </label>
            <div class="size-slider-container">
              <input
                id="size-slider"
                type="range"
                min="1"
                max="5"
                :value="sizeSliderValue"
                @input="handleSizeChange"
                class="size-slider"
                aria-label="Adjust thumbnail size"
              />
              <div class="size-indicators">
                <span
                  v-for="(size, index) in SIZE_OPTIONS"
                  :key="size.value"
                  :class="['size-indicator', { active: thumbnailSize === size.value }]"
                  :style="{ left: `${(index / (SIZE_OPTIONS.length - 1)) * 100}%` }"
                  :title="size.label"
                ></span>
              </div>
            </div>
            <div class="size-display">
              {{ currentSizeLabel }}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { Squares2X2Icon } from '@heroicons/vue/24/outline';
import type { ThumbnailSize, UpdateEvent } from '../../types/dashboard';
import { SIZE_OPTIONS } from '../../constants/dashboard';

interface DashboardViewControlsProps {
  thumbnailSize: ThumbnailSize;
  resultsCount: number;
  hasActiveFilters: boolean;
}

interface DashboardViewControlsEmits {
  'update:thumbnailSize': UpdateEvent<ThumbnailSize>;
}

// Props
const props = defineProps<DashboardViewControlsProps>();

// Emits
const emit = defineEmits<DashboardViewControlsEmits>();

// Computed properties
const sizeSliderValue = computed(() => {
  return SIZE_OPTIONS.findIndex(option => option.value === props.thumbnailSize) + 1;
});

const currentSizeLabel = computed(() => {
  return SIZE_OPTIONS.find(option => option.value === props.thumbnailSize)?.label || 'SM';
});

// Methods
const handleSizeChange = (event: Event) => {
  const target = event.target as HTMLInputElement;
  const sliderValue = parseInt(target.value);
  const selectedSize = SIZE_OPTIONS[sliderValue - 1];
  if (selectedSize) {
    emit('update:thumbnailSize', selectedSize.value);
  }
};
</script>

<style scoped>
.dashboard-view-controls {
  margin-bottom: 16px;
}

.header-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 16px;
  flex-wrap: wrap;
}

.results-info {
  display: flex;
  align-items: baseline;
  gap: 6px;
  color: rgba(255, 255, 255, 0.9);
  font-weight: 500;
}

.results-count {
  font-size: 1.25rem;
  font-weight: 700;
  color: #10b981;
}

.results-text {
  font-size: 1rem;
  color: rgba(255, 255, 255, 0.8);
}

.results-filtered {
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.6);
  font-style: italic;
}

.view-controls {
  display: flex;
  align-items: center;
  gap: 16px;
}

.thumbnail-size-controls {
  display: flex;
  align-items: center;
  gap: 12px;
}

.size-control-group {
  display: flex;
  align-items: center;
  gap: 12px;
  background: rgba(255, 255, 255, 0.1);
  padding: 8px 16px;
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
}

.size-label {
  display: flex;
  align-items: center;
  cursor: pointer;
}

.size-icon {
  width: 20px;
  height: 20px;
  color: rgba(255, 255, 255, 0.7);
}

.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.size-slider-container {
  position: relative;
  width: 120px;
  height: 24px;
  display: flex;
  align-items: center;
}

.size-slider {
  width: 100%;
  height: 4px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 2px;
  outline: none;
  cursor: pointer;
  -webkit-appearance: none;
  appearance: none;
}

.size-slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 16px;
  height: 16px;
  background: #10b981;
  border-radius: 50%;
  cursor: pointer;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.size-slider::-webkit-slider-thumb:hover {
  transform: scale(1.1);
  box-shadow: 0 4px 8px rgba(16, 185, 129, 0.3);
}

.size-slider::-moz-range-thumb {
  width: 16px;
  height: 16px;
  background: #10b981;
  border-radius: 50%;
  cursor: pointer;
  border: none;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.size-indicators {
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 4px;
  pointer-events: none;
  transform: translateY(-50%);
}

.size-indicator {
  position: absolute;
  width: 6px;
  height: 6px;
  background: rgba(255, 255, 255, 0.4);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.size-indicator.active {
  background: #10b981;
  transform: translate(-50%, -50%) scale(1.2);
}

.size-display {
  min-width: 24px;
  text-align: center;
  font-size: 0.85rem;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.9);
}

/* Responsive Design */
@media (max-width: 768px) {
  .header-controls {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }

  .results-info {
    justify-content: center;
  }

  .view-controls {
    justify-content: center;
  }

  .size-control-group {
    padding: 6px 12px;
  }

  .size-slider-container {
    width: 100px;
  }
}

@media (max-width: 480px) {
  .size-control-group {
    gap: 8px;
    padding: 4px 8px;
  }

  .size-slider-container {
    width: 80px;
  }

  .size-icon {
    width: 18px;
    height: 18px;
  }

  .size-display {
    font-size: 0.8rem;
    min-width: 20px;
  }
}
</style>