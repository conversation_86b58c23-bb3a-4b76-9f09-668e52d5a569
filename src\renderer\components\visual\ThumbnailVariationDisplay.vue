<template>
  <div class="thumbnail-variation-display">
    <!-- Primary Thumbnail -->
    <div class="primary-thumbnail">
      <img
        :src="selectedThumbnail.imageData"
        :alt="`${modName} - ${selectedThumbnail.resourceType}`"
        class="primary-image"
        @load="handleImageLoad"
        @error="handleImageError"
      />
      
      <!-- Swatch Count Badge -->
      <div v-if="hasVariations" class="variation-count-badge">
        {{ totalVariations }}
      </div>
      
      <!-- Quality Indicator -->
      <div v-if="selectedThumbnail.isHighQuality" class="quality-badge">
        <SparklesIcon class="w-3 h-3" />
      </div>
    </div>
    
    <!-- Swatch Grid (when expanded) -->
    <div v-if="showVariations && hasVariations" class="variation-grid" :class="gridSizeClass">
      <div
        v-for="(thumbnail, index) in displayedVariations"
        :key="thumbnail.id"
        class="variation-item"
        :class="{ 
          'selected': selectedThumbnail.id === thumbnail.id,
          'loading': !thumbnail.loaded 
        }"
        @click="selectThumbnail(thumbnail)"
        @mouseenter="handleVariationHover(thumbnail)"
        @mouseleave="handleVariationLeave"
      >
        <img
          :src="thumbnail.imageData"
          :alt="`${modName} - Swatch ${index + 1}`"
          class="variation-image"
          loading="lazy"
          @load="() => markThumbnailLoaded(thumbnail)"
          @error="() => markThumbnailError(thumbnail)"
        />
        
        <!-- Swatch Info -->
        <div class="variation-info">
          <span class="variation-type">{{ thumbnail.resourceType }}</span>
          <span class="variation-method">{{ thumbnail.extractionMethod }}</span>
        </div>
      </div>
      
      <!-- Show More Button -->
      <div v-if="hasMoreVariations" class="show-more-button" @click="expandAllVariations">
        <PlusIcon class="w-4 h-4" />
        <span>+{{ remainingVariations }}</span>
      </div>
    </div>
    
    <!-- Swatch Controls -->
    <div v-if="hasVariations" class="variation-controls">
      <button
        class="toggle-variations-btn"
        @click="toggleVariations"
        :aria-expanded="showVariations"
      >
        <ChevronDownIcon 
          class="w-4 h-4 transition-transform"
          :class="{ 'rotate-180': showVariations }"
        />
        <span>{{ showVariations ? 'Hide' : 'Show' }} Swatches</span>
      </button>
      
      <!-- Display Mode Toggle -->
      <div class="display-mode-toggle">
        <button
          v-for="mode in displayModes"
          :key="mode.value"
          class="mode-btn"
          :class="{ 'active': displayMode === mode.value }"
          @click="setDisplayMode(mode.value)"
          :title="mode.label"
        >
          <component :is="mode.icon" class="w-4 h-4" />
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import {
  SparklesIcon,
  ChevronDownIcon,
  PlusIcon,
  Squares2X2Icon,
  ViewColumnsIcon,
  QueueListIcon
} from '@heroicons/vue/24/outline';

import type { ThumbnailData } from '../../../services/visual/ThumbnailExtractionService';
import type { ThumbnailDisplayMode } from '../../../types/ModData';

// Props
interface Props {
  modName: string;
  primaryThumbnail: ThumbnailData;
  variations: ThumbnailData[];
  displayMode?: ThumbnailDisplayMode;
  maxVisibleVariations?: number;
  showVariationCount?: boolean;
  enableHoverPreview?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  displayMode: 'grid',
  maxVisibleVariations: 8,
  showVariationCount: true,
  enableHoverPreview: true
});

// Emits
const emit = defineEmits<{
  thumbnailSelected: [thumbnail: ThumbnailData];
  variationHover: [thumbnail: ThumbnailData];
  variationLeave: [];
}>();

// Reactive state
const selectedThumbnail = ref<ThumbnailData>(props.primaryThumbnail);
const showVariations = ref(false);
const showAllVariations = ref(false);
const displayMode = ref<ThumbnailDisplayMode>(props.displayMode);
const loadedThumbnails = ref(new Set<string>());

// Computed properties
const hasVariations = computed(() => props.variations.length > 0);

const totalVariations = computed(() => props.variations.length); // Just the variations, not including primary

const displayedVariations = computed(() => {
  const variations = showAllVariations.value
    ? props.variations
    : props.variations.slice(0, props.maxVisibleVariations);

  console.log(`🎨 [ThumbnailVariationDisplay] displayedVariations:`, {
    totalVariations: props.variations.length,
    displayedCount: variations.length,
    primaryThumbnailId: props.primaryThumbnail?.id,
    selectedThumbnailId: selectedThumbnail.value?.id,
    variationIds: variations.map(v => v.id),
    isPrimaryInDisplayed: variations.some(v => v.id === props.primaryThumbnail?.id)
  });

  return variations;
});

const hasMoreVariations = computed(() => 
  !showAllVariations.value && props.variations.length > props.maxVisibleVariations
);

const remainingVariations = computed(() => 
  props.variations.length - props.maxVisibleVariations
);

const gridSizeClass = computed(() => {
  const count = displayedVariations.value.length;
  if (count <= 4) return 'grid-small';
  if (count <= 8) return 'grid-medium';
  return 'grid-large';
});

const displayModes = [
  { value: 'grid', label: 'Grid View', icon: Squares2X2Icon },
  { value: 'carousel', label: 'Carousel View', icon: ViewColumnsIcon },
  { value: 'expandable', label: 'Expandable View', icon: QueueListIcon }
];

// Methods
const selectThumbnail = (thumbnail: ThumbnailData) => {
  selectedThumbnail.value = thumbnail;
  emit('thumbnailSelected', thumbnail);
};

const toggleVariations = () => {
  showVariations.value = !showVariations.value;
};

const setDisplayMode = (mode: ThumbnailDisplayMode) => {
  displayMode.value = mode;
};

const expandAllVariations = () => {
  showAllVariations.value = true;
  console.log(`🔍 [ThumbnailVariationDisplay] Showing all ${props.variations.length} swatches`);
};

const handleVariationHover = (thumbnail: ThumbnailData) => {
  if (props.enableHoverPreview) {
    emit('variationHover', thumbnail);
  }
};

const handleVariationLeave = () => {
  if (props.enableHoverPreview) {
    emit('variationLeave');
  }
};

const markThumbnailLoaded = (thumbnail: ThumbnailData) => {
  loadedThumbnails.value.add(thumbnail.id);
};

const markThumbnailError = (thumbnail: ThumbnailData) => {
  console.warn(`Failed to load thumbnail swatch: ${thumbnail.id}`);
};

const handleImageLoad = () => {
  // Handle primary image load
};

const handleImageError = () => {
  console.warn(`Failed to load primary thumbnail for ${props.modName}`);
};

// Watch for prop changes
watch(() => props.primaryThumbnail, (newThumbnail) => {
  selectedThumbnail.value = newThumbnail;
});
</script>

<style scoped>
/* ===== PROFESSIONAL THUMBNAIL SWATCHES DISPLAY ===== */
/* Apple-inspired design with Simonitor design system integration */

.thumbnail-variation-display {
  position: relative;
  width: 100%;
}

/* ===== PRIMARY THUMBNAIL SECTION ===== */
.primary-thumbnail {
  position: relative;
  border-radius: var(--radius-lg);
  overflow: hidden;
  background: var(--bg-elevated);
  border: 1px solid var(--border-light);
  box-shadow: var(--shadow-sm);
  width: 100%;
  max-width: 400px; /* Increased from 280px to better utilize available space */
  aspect-ratio: 1;
  margin: 0 auto;
}

.primary-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
}

/* ===== SWATCH BADGES ===== */
.variation-count-badge {
  position: absolute;
  top: var(--space-2);
  right: var(--space-2);
  background: var(--primary);
  color: var(--primary-foreground);
  font-size: var(--text-xs);
  font-weight: var(--font-semibold);
  padding: var(--space-1) var(--space-2);
  border-radius: var(--radius-full);
  box-shadow: var(--shadow-sm);
  backdrop-filter: blur(8px);
  z-index: 10;
}

.quality-badge {
  position: absolute;
  top: var(--space-2);
  left: var(--space-2);
  background: var(--warning);
  color: var(--warning-foreground);
  padding: var(--space-1);
  border-radius: var(--radius-full);
  box-shadow: var(--shadow-sm);
  backdrop-filter: blur(8px);
  z-index: 10;
}

/* ===== SWATCHES GRID LAYOUT ===== */
.variation-grid {
  margin-top: var(--space-4);
  display: grid;
  gap: var(--space-3);
  width: 100%;
  /* Remove max-width constraints to utilize full available horizontal space */
}

.grid-small {
  grid-template-columns: repeat(3, 1fr);
  /* Removed max-width constraint - use full available space */
}

.grid-medium {
  grid-template-columns: repeat(4, 1fr);
  /* Removed max-width constraint - use full available space */
}

.grid-large {
  grid-template-columns: repeat(5, 1fr); /* Increased from 4 to 5 columns for better space utilization */
  /* Removed max-width constraint - use full available space */
}

@media (min-width: 640px) {
  .grid-small {
    grid-template-columns: repeat(4, 1fr);
    /* Removed max-width constraint - use full available space */
  }

  .grid-medium {
    grid-template-columns: repeat(5, 1fr); /* Increased from 4 to 5 columns for better space utilization */
    /* Removed max-width constraint - use full available space */
  }

  .grid-large {
    grid-template-columns: repeat(6, 1fr); /* Increased from 4 to 6 columns for better space utilization */
    /* Removed max-width constraint - use full available space */
  }
}

/* ===== SWATCH ITEMS ===== */
.variation-item {
  position: relative;
  cursor: pointer;
  border-radius: var(--radius-md);
  overflow: hidden;
  background: var(--bg-elevated);
  border: 2px solid var(--border-light);
  aspect-ratio: 1;
  transition: all var(--duration-150) var(--ease-out);
  transform: translateZ(0); /* GPU acceleration */
}

.variation-item:hover {
  border-color: var(--primary-border);
  box-shadow: var(--shadow-sm);
  transform: translateY(-1px) scale(1.02);
}

.variation-item.selected {
  border-color: var(--primary);
  box-shadow: 0 0 0 1px var(--primary), var(--shadow-md);
  transform: translateY(-1px);
}

.variation-item.loading {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  background: linear-gradient(90deg, var(--bg-secondary) 25%, var(--bg-tertiary) 50%, var(--bg-secondary) 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s ease-in-out infinite;
}

@keyframes shimmer {
  0% { background-position: -200% 0; }
  100% { background-position: 200% 0; }
}

.variation-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
  transition: transform var(--duration-150) var(--ease-out);
}

.variation-item:hover .variation-image {
  transform: scale(1.05);
}

/* ===== SWATCH INFO OVERLAY ===== */
.variation-info {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(to top, rgba(0, 0, 0, 0.8), transparent);
  color: var(--neutral-50);
  font-size: var(--text-xs);
  padding: var(--space-2);
  opacity: 0;
  transition: opacity var(--duration-200) var(--ease-out);
  backdrop-filter: blur(4px);
}

.variation-item:hover .variation-info {
  opacity: 1;
}

.variation-type {
  display: block;
  font-weight: var(--font-medium);
  line-height: 1.2;
}

.variation-method {
  display: block;
  color: var(--neutral-300);
  margin-top: var(--space-1);
}

/* ===== SHOW MORE BUTTON ===== */
.show-more-button {
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--bg-secondary);
  border: 2px dashed var(--border-medium);
  border-radius: var(--radius-md);
  cursor: pointer;
  aspect-ratio: 1;
  color: var(--text-secondary);
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  transition: all var(--duration-200) var(--ease-out);
}

.show-more-button:hover {
  background: var(--bg-tertiary);
  border-color: var(--border-strong);
  color: var(--text-primary);
  transform: translateY(-1px);
}

/* ===== SWATCH CONTROLS ===== */
.variation-controls {
  margin-top: var(--space-4);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--space-3);
  background: var(--bg-secondary);
  border-radius: var(--radius-md);
  border: 1px solid var(--border-light);
}

.toggle-variations-btn {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-2) var(--space-3);
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  color: var(--text-secondary);
  background: transparent;
  border: none;
  border-radius: var(--radius-sm);
  cursor: pointer;
  transition: all var(--duration-200) var(--ease-out);
}

.toggle-variations-btn:hover {
  color: var(--text-primary);
  background: var(--bg-tertiary);
}

.display-mode-toggle {
  display: flex;
  gap: var(--space-1);
  background: var(--bg-primary);
  padding: var(--space-1);
  border-radius: var(--radius-sm);
  border: 1px solid var(--border-light);
}

.mode-btn {
  padding: var(--space-1);
  border-radius: var(--radius-xs);
  color: var(--text-muted);
  background: transparent;
  border: none;
  cursor: pointer;
  transition: all var(--duration-150) var(--ease-out);
  display: flex;
  align-items: center;
  justify-content: center;
}

.mode-btn:hover {
  color: var(--text-secondary);
  background: var(--bg-secondary);
}

.mode-btn.active {
  color: var(--primary);
  background: var(--primary-bg);
  box-shadow: var(--shadow-xs);
}
</style>
