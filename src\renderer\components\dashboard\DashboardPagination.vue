<template>
  <nav v-if="totalPages > 1" class="pagination" aria-label="Pagination navigation">
    <button
      @click="goToPreviousPage"
      :disabled="currentPage === 1"
      class="pagination-btn pagination-btn--prev"
      aria-label="Previous page"
    >
      <ChevronLeftIcon class="pagination-icon" />
      Previous
    </button>

    <div class="pagination-info">
      <span class="pagination-text">
        Page {{ currentPage }} of {{ totalPages }}
      </span>
      <span class="pagination-details">
        ({{ startIndex + 1 }}-{{ endIndex }} of {{ totalItems }} items)
      </span>
    </div>

    <button
      @click="goToNextPage"
      :disabled="currentPage === totalPages"
      class="pagination-btn pagination-btn--next"
      aria-label="Next page"
    >
      Next
      <ChevronRightIcon class="pagination-icon" />
    </button>
  </nav>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { ChevronLeftIcon, ChevronRightIcon } from '@heroicons/vue/24/outline';

interface DashboardPaginationProps {
  currentPage: number;
  totalPages: number;
  totalItems: number;
  itemsPerPage: number;
}

interface DashboardPaginationEmits {
  'update:currentPage': [page: number];
  'page-change': [page: number];
}

// Props
const props = defineProps<DashboardPaginationProps>();

// Emits
const emit = defineEmits<DashboardPaginationEmits>();

// Computed properties
const startIndex = computed(() =>
  (props.currentPage - 1) * props.itemsPerPage
);

const endIndex = computed(() =>
  Math.min(startIndex.value + props.itemsPerPage, props.totalItems)
);

// Methods
const goToPreviousPage = () => {
  if (props.currentPage > 1) {
    const newPage = props.currentPage - 1;
    emit('update:currentPage', newPage);
    emit('page-change', newPage);
  }
};

const goToNextPage = () => {
  if (props.currentPage < props.totalPages) {
    const newPage = props.currentPage + 1;
    emit('update:currentPage', newPage);
    emit('page-change', newPage);
  }
};
</script>

<style scoped>
.pagination {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 24px;
  margin-top: 32px;
  padding: 20px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  backdrop-filter: blur(10px);
}

.pagination-btn {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 10px;
  color: white;
  font-weight: 600;
  font-size: 0.95rem;
  cursor: pointer;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(10px);
  min-width: 100px;
  justify-content: center;
}

.pagination-btn:hover:not(:disabled) {
  background: rgba(255, 255, 255, 0.15);
  border-color: rgba(255, 255, 255, 0.3);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.pagination-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none !important;
  box-shadow: none !important;
}

.pagination-btn--prev {
  flex-direction: row;
}

.pagination-btn--next {
  flex-direction: row-reverse;
}

.pagination-icon {
  width: 18px;
  height: 18px;
  flex-shrink: 0;
}

.pagination-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  text-align: center;
  min-width: 200px;
}

.pagination-text {
  font-size: 1rem;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.9);
}

.pagination-details {
  font-size: 0.85rem;
  color: rgba(255, 255, 255, 0.6);
  font-weight: 500;
}

/* Responsive Design */
@media (max-width: 768px) {
  .pagination {
    flex-direction: column;
    gap: 16px;
    padding: 16px;
  }

  .pagination-btn {
    width: 100%;
    max-width: 200px;
    padding: 10px 16px;
    font-size: 0.9rem;
  }

  .pagination-info {
    order: -1;
    min-width: auto;
  }

  .pagination-text {
    font-size: 0.95rem;
  }

  .pagination-details {
    font-size: 0.8rem;
  }
}

@media (max-width: 480px) {
  .pagination {
    gap: 12px;
    padding: 12px;
  }

  .pagination-btn {
    padding: 8px 12px;
    font-size: 0.85rem;
    min-width: 80px;
  }

  .pagination-icon {
    width: 16px;
    height: 16px;
  }

  .pagination-text {
    font-size: 0.9rem;
  }

  .pagination-details {
    font-size: 0.75rem;
  }
}
</style>